import { initializeDatabase } from "./src/lib/database";
import { loadJobDefinition, saveJobDefinition } from "./src/lib/jobPersistence";

async function fixDatabaseAdminJob() {
  try {
    console.log("🔧 Fixing Database Admin Job Configuration...\n");

    console.log("Initializing database...");
    await initializeDatabase();

    const jobId = "3";
    console.log(`Loading job: ${jobId}`);
    const job = await loadJobDefinition(jobId);

    if (!job) {
      console.log(`❌ Job ${jobId} not found`);
      return;
    }

    console.log(`📄 Current job: ${job.name}`);
    console.log(
      `Current dataSource config:`,
      JSON.stringify(job.dataSource, null, 2)
    );

    // Check if the job is a database_admin type and missing operationMode
    if (
      job.dataSource.type === "database_admin" &&
      job.dataSource.database_admin
    ) {
      const dbAdminConfig = job.dataSource.database_admin;

      if (!dbAdminConfig.operationMode) {
        console.log("❌ Missing operationMode in database_admin configuration");
        console.log('🔧 Adding operationMode: "table_management"');

        // Add the missing operationMode field
        const updatedJob = {
          ...job,
          dataSource: {
            ...job.dataSource,
            database_admin: {
              ...dbAdminConfig,
              operationMode: "table_management",
            },
          },
        };

        console.log("💾 Saving updated job configuration...");
        await saveJobDefinition(updatedJob);

        console.log("✅ Job configuration updated successfully!");
        console.log(
          "Updated dataSource config:",
          JSON.stringify(updatedJob.dataSource, null, 2)
        );
      } else {
        console.log(
          "✅ Job already has operationMode configured:",
          dbAdminConfig.operationMode
        );
      }
    } else {
      console.log(
        "ℹ️  Job is not a database_admin type or missing database_admin config"
      );
    }
  } catch (error) {
    console.error("❌ Error fixing database admin job:", error);
    throw error;
  }
}

// Run the fix
fixDatabaseAdminJob()
  .then(() => {
    console.log("\n🎉 Database admin job fix completed successfully!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("\n💥 Failed to fix database admin job:", error);
    process.exit(1);
  });
