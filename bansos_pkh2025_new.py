#!D:/python37/python.exe
print ("Content-type: text/html\n")

import cx_Oracle
import mysql.connector
import logging
import sys
from datetime import datetime
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bansos_pkh2025_migration.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

logger.info("=== PKH 2025 Data Migration Started ===")
logger.info(f"Script started at: {datetime.now()}")

try:
    #koneksi mysql
    logger.info("Attempting to connect to MySQL database...")
    logger.info("MySQL Host: **************, Database: monev2025, Port: 3352")

    db = mysql.connector.connect(
        host="**************",
        user="root",
        password="dabantek2018",
        database="monev2025",
        port="3352")
    curMysql = db.cursor()
    logger.info("✓ MySQL connection established successfully")

    # Truncate existing data
    logger.info("Truncating existing data from monev2025.bansos_pkh table...")
    SQL_truncate ="TRUNCATE monev2025.bansos_pkh"
    curMysql.execute(SQL_truncate)
    logger.info("✓ Table truncated successfully")

    # Prepare insert statement
    SQL_isi = "INSERT INTO monev2025.bansos_pkh(jenis_transaksi,kode_lokasi,kdsatker,tgl_sp2d,tahap,return_code,jml,nilai) \
                VALUES(%s,%s,%s,%s,%s,%s,%s,%s)"
    logger.info("Insert statement prepared")

    #koneksi oracle
    logger.info("Attempting to connect to Oracle database...")
    logger.info("Oracle Connection: USRPA@**************:1521/olap23")
    dbOracle = cx_Oracle.Connection("USRPA/pdpsipa@**************:1521/olap23")
    curOracle = dbOracle.cursor()
    logger.info("✓ Oracle connection established successfully")

    # Execute Oracle query
    logger.info("Executing Oracle query to fetch PKH 2025 data...")
    SQL = "SELECT jenis_transaksi,kode_lokasi,kdsatker,to_char(tgl_sp2d,'yyyy-mm-dd') as tgl_sp2d,tahap,return_code,count(no_rek) as jml, \
            sum(nilai) as nilai from bansos_pkh where TO_CHAR(tgl_sp2d,'yyyy')='2025' \
            group by jenis_transaksi,kode_lokasi,kdsatker,tgl_sp2d,tahap,return_code"
    logger.info(f"Query: {SQL}")

    curOracle.execute(SQL)
    data = curOracle.fetchall()
    jml_rec = len(data)
    logger.info(f"✓ Query executed successfully. Found {jml_rec} records to process")

    # Process and insert data
    if jml_rec == 0:
        logger.warning("No records found for year 2025. Migration completed with 0 records.")
    else:
        logger.info("Starting data insertion process...")
        count = 1
        successful_inserts = 0
        failed_inserts = 0

        for row in data:
            try:
                # Log progress every 100 records or for small datasets every 10 records
                if jml_rec <= 50 or count % 100 == 0 or count == 1:
                    logger.info(f"Processing record {count}/{jml_rec}: {row[0]}, {row[1]}, {row[2]}, {row[3]}")

                curMysql.execute(SQL_isi, (row[0], row[1], row[2], row[3], row[4], row[5], row[6], row[7]))
                successful_inserts += 1

                # Log detailed info for first few records
                if count <= 5:
                    logger.info(f"✓ Record {count} inserted successfully: jenis_transaksi={row[0]}, kode_lokasi={row[1]}, kdsatker={row[2]}, tgl_sp2d={row[3]}, tahap={row[4]}, return_code={row[5]}, jml={row[6]}, nilai={row[7]}")

            except Exception as e:
                failed_inserts += 1
                logger.error(f"✗ Failed to insert record {count}: {e}")
                logger.error(f"Record data: {row}")

            count += 1

        logger.info(f"Data insertion completed. Successful: {successful_inserts}, Failed: {failed_inserts}")

    # Commit MySQL transaction
    logger.info("Committing MySQL transaction...")
    db.commit()
    logger.info("✓ MySQL transaction committed successfully")

    # Close connections
    logger.info("Closing database connections...")
    dbOracle.close()
    logger.info("✓ Oracle connection closed")
    db.close()
    logger.info("✓ MySQL connection closed")

    logger.info("=== PKH 2025 Data Migration Completed Successfully ===")
    logger.info(f"Script completed at: {datetime.now()}")

except mysql.connector.Error as mysql_err:
    logger.error(f"MySQL Error: {mysql_err}")
    logger.error(f"Error Code: {mysql_err.errno}")
    logger.error(f"SQL State: {mysql_err.sqlstate}")
    logger.error(f"Error Message: {mysql_err.msg}")
    sys.exit(1)

except cx_Oracle.Error as oracle_err:
    logger.error(f"Oracle Error: {oracle_err}")
    error, = oracle_err.args
    logger.error(f"Oracle Error Code: {error.code}")
    logger.error(f"Oracle Error Message: {error.message}")
    sys.exit(1)

except Exception as e:
    logger.error(f"Unexpected error occurred: {e}")
    logger.error(f"Error type: {type(e).__name__}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    sys.exit(1)

finally:
    # Ensure connections are closed even if an error occurs
    try:
        if 'dbOracle' in locals() and dbOracle:
            dbOracle.close()
            logger.info("Oracle connection closed in finally block")
    except:
        pass

    try:
        if 'db' in locals() and db:
            db.close()
            logger.info("MySQL connection closed in finally block")
    except:
        pass

